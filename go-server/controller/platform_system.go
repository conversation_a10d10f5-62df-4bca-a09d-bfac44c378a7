package controller

import (
	"fmt"
	"net"
	"net/http"
	"os/exec"
	"strconv"
	"strings"
	"sync"

	"github.com/duke-git/lancet/v2/slice"
	"github.com/gin-gonic/gin"
	di "github.com/hakpaang/debinterface"
	"github.com/shirou/gopsutil/cpu"
	"github.com/shirou/gopsutil/disk"
	"github.com/shirou/gopsutil/mem"
	"github.com/sourcegraph/conc"
	"github.com/sourcegraph/conc/iter"
	"mediacomm.com/skylink/global"
	"mediacomm.com/skylink/model/system"
	"mediacomm.com/skylink/utils"
)

var (
	sysReqLock sync.Mutex
	sysReqWg   conc.WaitGroup
)

func GetSystemStatusInfo(c *gin.Context) {
	sysReqLock.Lock()
	defer sysReqLock.Unlock()
	res := new(system.SysInfo)
	var interfaces []string
	interfaces, err := utils.GetPhysicalInterfaces()
	if err != nil {
		global.GvaLog.Error(err)
		c.J<PERSON>(http.StatusForbidden, gin.H{"message": err.Error()})
		return
	}
	sysReqWg.Go(func() {
		iter.ForEach(interfaces, func(val *string) {
			connected, err := isNetDevUp(*val)
			if err != nil {
				global.GvaLog.Error(err.Error())
			}
			addressList, err := getLocalIp(*val)
			if err != nil {
				global.GvaLog.Error(err.Error())
			}
			net := system.Network{Name: *val, Connected: connected, Ip: addressList}
			res.Network = append(res.Network, net)
		})
	})
	sysReqWg.Go(func() {
		gateway, err := getGateway()
		if err != nil {
			global.GvaLog.Error(err.Error())
		}
		res.Gateway = gateway
	})

	sysReqWg.Go(func() {
		physicalCnt, _ := cpu.Counts(false)
		logicalCnt, _ := cpu.Counts(true)
		res.Cpu.Cores = strconv.Itoa(physicalCnt) + "/" + strconv.Itoa(logicalCnt)
	})
	sysReqWg.Go(func() {
		totalPercent, _ := cpu.Percent(0, false)
		res.Cpu.Used, _ = strconv.ParseFloat(fmt.Sprintf("%.2f", totalPercent[0]), 64)
	})
	sysReqWg.Go(func() {
		usage, _ := disk.Usage("/")
		res.Disk.Used = usage.Used / (1024 * 1024)
		res.Disk.Total = usage.Total / (1024 * 1024)
	})
	sysReqWg.Go(func() {
		memory, _ := mem.VirtualMemory()
		res.Ram.Used = memory.Used / (1024 * 1024)
		res.Ram.Total = memory.Total / (1024 * 1024)
	})
	sysReqWg.Wait()
	c.JSON(http.StatusOK, res)
}

func getGateway() (string, error) {
	// cmdstr := "ip route |grep default|awk '{print $3}'"
	cmd := exec.Command("nsenter", "-m", "-n", "-t", "1", "ip", "route")
	out, err := cmd.CombinedOutput()
	if err != nil {
		return "", fmt.Errorf("返回值: %s. %w", string(out), err)
	}
	allResponceList := slice.Compact(strings.Split(string(out), "\n"))
	var responce string = ""
	for _, res := range allResponceList {
		if strings.Contains(res, "default") {
			responce = res
			break
		}
	}
	responceList := slice.Compact(strings.Split(string(responce), " "))
	if len(responceList) < 4 {
		return "", fmt.Errorf("找不到默认网关")
	}
	// responce = strings.Replace(string(out), "\n", "", -1)
	return responceList[2], nil
}

func isNetDevUp(name string) (bool, error) {
	cmd := exec.Command("nsenter", "-m", "-n", "-t", "1", "ip", "link", "show")
	out, err := cmd.CombinedOutput()
	if err != nil {
		return false, fmt.Errorf("返回值: %s. %w", string(out), err)
	}
	allResponceList := slice.Compact(strings.Split(string(out), "\n"))
	var responce string = ""
	for _, res := range allResponceList {
		if strings.Contains(res, name) {
			responce = res
			break
		}
	}
	if responce == "" {
		return false, fmt.Errorf("找不到网卡: %s", name)
	}
	start := strings.Index(responce, "<")
	end := strings.Index(responce, ">")
	netStatus := responce[start+1 : end]
	connected := slice.Contain(strings.Split(netStatus, ","), "LOWER_UP")
	return connected, nil
}

func getLocalIp(name string) ([]string, error) {
	// cmdstr := fmt.Sprintf("ip addr show %s|grep 'inet '|awk '{print $2}'", name)
	cmd := exec.Command("nsenter", "-m", "-n", "-t", "1", "ip", "addr", "show", name)
	out, err := cmd.CombinedOutput()
	responce := string(out)
	if err != nil {
		return nil, fmt.Errorf("返回值: %s. %w", responce, err)
	}
	allResponceList := slice.Compact(strings.Split(responce, "\n"))
	var addressList []string
	for _, res := range allResponceList {
		if strings.Contains(res, "inet") {
			responceList := slice.Compact(strings.Split(res, " "))
			if len(responceList) > 1 {
				addressList = append(addressList, responceList[1])
			}
		}
	}
	return addressList, nil
}

// 更改宿主机网络配置
func ChangeHostNetwork(c *gin.Context) {
	// 提取请求体
	var vals system.NetworkInterfaces
	if err := c.ShouldBindJSON(&vals); err != nil {
		global.GvaLog.Error(err.Error())
		c.JSON(http.StatusBadRequest, gin.H{"message": err.Error()})
		return
	}
	fmt.Println(utils.BeautifyStruct(vals))
	if !vals.Validate() {
		global.GvaLog.Error("request validate failed")
		c.JSON(http.StatusBadRequest, gin.H{"message": "request validate failed"})
		return
	}
	netDevList, err := utils.GetPhysicalInterfaces()
	if err != nil {
		global.GvaLog.Error(err.Error())
		c.JSON(http.StatusBadRequest, gin.H{"message": err.Error()})
		return
	}
	global.GvaLog.Info("ChangeHostNetwork:\n" + utils.BeautifyStruct(vals))
	var change bool
	// var changeDev []string
	for _, v := range vals {
		if slice.Contain(netDevList, v.Name) {
			change = true
			// changeDev = append(changeDev, v.Name)
			var proto di.Protocol
			if v.DHCP {
				proto = di.DHCP
			} else {
				proto = di.STATIC
			}
			adapter := &di.Adapter{}
			adapter.Protocol = proto
			adapter.Name = v.Name
			adapter.Auto = true
			adapter.Family = di.INET
			adapter.Address = net.ParseIP(v.Address)
			adapter.Netmask = net.ParseIP(v.Netmask)
			adapter.Gateway = net.ParseIP(v.Gateway)
			intercesConf := &di.Interfaces{
				InterfacesPath: global.InterfacesPath + v.Name,
				Adapters:       []*di.Adapter{adapter},
			}
			if err = di.Marshal(intercesConf); err != nil {
				c.JSON(http.StatusBadRequest, gin.H{"message": "marshal interfaces config failed:" + err.Error()})
				return
			}
		}
	}
	if !change {
		c.JSON(http.StatusAccepted, gin.H{"message": "no network interface change"})
		return
	}
	// 重启网络服务
	// nsenter -m  -t 1  /etc/init.d/networking restart
	// cmdstr := "/etc/init.d/networking restart"
	cmd := exec.Command("nsenter", "-m", "-t", "1", "/etc/init.d/networking", "restart")
	out, err := cmd.CombinedOutput()
	if err != nil {
		global.GvaLog.Errorf("Command execution failed: %v", err)
	}
	c.JSON(http.StatusAccepted, gin.H{"message": string(out)})
}

// 远程关闭宿主机
func ShutdownHost(c *gin.Context) {
	// cmdstr := "shutdown now"
	cmd := exec.Command("nsenter", "-m", "-t", "1", "shutdown", "now")
	_, err := cmd.CombinedOutput()
	if err != nil {
		global.GvaLog.Errorf("Command execution failed: %v", err)
	}
}

// 远程重启宿主机
func RebootHost(c *gin.Context) {
	cmdstr := "reboot"
	cmd := exec.Command("nsenter", "-m", "-t", "1", cmdstr)
	_, err := cmd.CombinedOutput()
	if err != nil {
		global.GvaLog.Errorf("Command execution failed: %v", err)
	}
}

func getNoVirtualInterfaces() ([]string, error) {
	cmdAll := exec.Command("nsenter", "-m", "-t", "1", "ls", "/sys/class/net/")
	responce, err := cmdAll.CombinedOutput()
	if err != nil {
		global.GvaLog.Errorf("Command execution failed: %s %v", responce, err)
		return nil, err
	}
	allNetList := slice.Compact(strings.Split(string(responce), "\n"))
	cmdExclude := exec.Command("nsenter", "-m", "-t", "1", "ls", "/sys/devices/virtual/net/")
	responce, err = cmdExclude.CombinedOutput()
	if err != nil {
		global.GvaLog.Errorf("Command execution failed: %s %v", responce, err)
		return nil, err
	}
	excludeNetList := slice.Compact(strings.Split(string(responce), "\n"))
	comboStrList := slice.SymmetricDifference(allNetList, excludeNetList)
	return comboStrList, nil
}
