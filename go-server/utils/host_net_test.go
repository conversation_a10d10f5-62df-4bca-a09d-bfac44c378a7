package utils

import (
	"testing"

	"mediacomm.com/skylink/model/system"
)

func TestGenerateFixedIP(t *testing.T) {
	tests := []struct {
		name          string
		interfaceName string
		wantPattern   string // 期望的IP模式，如 "***********/24"
		wantErr       bool
	}{
		{
			name:          "Valid interface name",
			interfaceName: "enaphyt4i0",
			wantPattern:   "10.10.*.10/24", // * 表示任意数字
			wantErr:       false,
		},
		{
			name:          "Invalid interface name",
			interfaceName: "nonexistent",
			wantPattern:   "",
			wantErr:       true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, err := generateFixedIP(tt.interfaceName)
			if (err != nil) != tt.wantErr {
				t.Errorf("generateFixedIP() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !tt.wantErr && got == "" {
				t.<PERSON>("generateFixedIP() returned empty string when expecting valid IP")
			}
			if !tt.wantErr {
				t.Logf("Generated fixed IP for %s: %s", tt.interfaceName, got)
			}
		})
	}
}

func TestDetectNetworkManager(t *testing.T) {
	manager, err := DetectNetworkManager()
	if err != nil {
		t.Logf("DetectNetworkManager() error = %v", err)
	} else {
		t.Logf("Detected network manager: %s", manager)
	}
}

func TestApplyHostNetworkConfig(t *testing.T) {
	// 这个测试需要在实际的容器环境中运行
	t.Skip("Skipping integration test - requires container environment with nsenter")

	configs := []system.NetworkInterface{
		{
			Name:    "enaphyt4i0",
			DHCP:    false,
			Address: "*************",
			Netmask: "*************",
			Gateway: "***********",
		},
	}

	err := ApplyHostNetworkConfig(configs)
	if err != nil {
		t.Errorf("ApplyHostNetworkConfig() error = %v", err)
	}
}

func TestNetmaskToCIDR(t *testing.T) {
	tests := []struct {
		name    string
		mask    string
		want    int
		wantErr bool
	}{
		{
			name:    "Valid /24 netmask",
			mask:    "*************",
			want:    24,
			wantErr: false,
		},
		{
			name:    "Valid /16 netmask",
			mask:    "***********",
			want:    16,
			wantErr: false,
		},
		{
			name:    "Valid /8 netmask",
			mask:    "*********",
			want:    8,
			wantErr: false,
		},
		{
			name:    "Invalid netmask",
			mask:    "invalid",
			want:    0,
			wantErr: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, err := netmaskToCIDR(tt.mask)
			if (err != nil) != tt.wantErr {
				t.Errorf("netmaskToCIDR() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if got != tt.want {
				t.Errorf("netmaskToCIDR() = %v, want %v", got, tt.want)
			}
		})
	}
}
